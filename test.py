from time import time
from network import Restormer_Encoder, Restormer_Decoder, LongScaleFeatureExtraction, ShortScaleFeatureExtraction
import os
import numpy as np
import cv2
from utils.Evaluator import Evaluator
import torch
import torch.nn as nn
from utils.img_read_save import img_save, image_read_cv2
import warnings
import logging
warnings.filterwarnings("ignore")
logging.basicConfig(level=logging.CRITICAL)

os.environ["CUDA_VISIBLE_DEVICES"] = "0"
ckpt_path='./models/CrossFuse_RSM3.pth'

# 初始化指标累积变量
all_metrics = {
    'EN': [], 'SD': [], 'SF': [], 'AG': [], 'MI': [], 'MSE': [],
    'CC': [], 'PSNR': [], 'SCD': [], 'VIFF': [], 'Qabf': [], 'SSIM': []
}

for dataset_name in ["RoadScene","TNO","MSRS"]:
    print("\n"*2+"="*80)
    model_name="CrossFuse    "
    print("The test result of "+dataset_name+' :')
    test_folder=os.path.join('./test_image/',dataset_name)
    test_out_folder=os.path.join('./test_results/',dataset_name)

    # 为当前数据集初始化指标列表
    dataset_metrics = {
        'EN': [], 'SD': [], 'SF': [], 'AG': [], 'MI': [], 'MSE': [],
        'CC': [], 'PSNR': [], 'SCD': [], 'VIFF': [], 'Qabf': [], 'SSIM': []
    }

    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    Encoder = nn.DataParallel(Restormer_Encoder()).to(device)
    Decoder = nn.DataParallel(Restormer_Decoder()).to(device)
    BaseFuseLayer = nn.DataParallel(LongScaleFeatureExtraction(dim=64, num_heads=8)).to(device)
    DetailFuseLayer = nn.DataParallel(ShortScaleFeatureExtraction(num_layers=1)).to(device)

    Encoder.load_state_dict(torch.load(ckpt_path)['DIDF_Encoder'])
    Decoder.load_state_dict(torch.load(ckpt_path)['DIDF_Decoder'])
    BaseFuseLayer.load_state_dict(torch.load(ckpt_path)['BaseFuseLayer'])
    DetailFuseLayer.load_state_dict(torch.load(ckpt_path)['DetailFuseLayer'])
    Encoder.eval()
    Decoder.eval()
    BaseFuseLayer.eval()
    DetailFuseLayer.eval()

    # 确保输出目录存在
    os.makedirs(test_out_folder, exist_ok=True)

    with torch.no_grad():
        sum = 0
        count = 0
        for img_name in os.listdir(os.path.join(test_folder,"ir")):

            data_IR = image_read_cv2(os.path.join(test_folder, "ir", img_name), mode='GRAY')[
                          np.newaxis, np.newaxis, ...] / 255.0
            data_VIS = cv2.split(image_read_cv2(os.path.join(test_folder, "vi", img_name), mode='YCrCb'))[0][
                           np.newaxis, np.newaxis, ...] / 255.0

            # ycrcb, uint8
            data_VIS_BGR = cv2.imread(os.path.join(test_folder, "vi", img_name))
            _, data_VIS_Cr, data_VIS_Cb = cv2.split(cv2.cvtColor(data_VIS_BGR, cv2.COLOR_BGR2YCrCb))


            data_IR,data_VIS = torch.FloatTensor(data_IR),torch.FloatTensor(data_VIS)
            data_VIS, data_IR = data_VIS.cuda(), data_IR.cuda()

            # add
            start_time = time()

            feature_V_L, feature_V_H, feature_V = Encoder(data_VIS)
            feature_I_L, feature_I_H, feature_I = Encoder(data_IR)
            feature_F_L = BaseFuseLayer(feature_V_L + feature_I_L)
            feature_F_H = DetailFuseLayer(feature_V_H + feature_I_H)

            Fuse, _ = Decoder(data_VIS, feature_F_L, feature_F_H)
            Fuse=(Fuse-torch.min(Fuse))/(torch.max(Fuse)-torch.min(Fuse))

            end_time = time()
            elapsed_time = end_time - start_time
            sum += elapsed_time
            count +=1

            fuse = np.squeeze((Fuse * 255).cpu().numpy())

            # float32 to uint8
            fuse = fuse.astype(np.uint8)
            # concatnate to get rgb results
            ycrcb_fuse = np.dstack((fuse, data_VIS_Cr, data_VIS_Cb))
            rgb_fuse = cv2.cvtColor(ycrcb_fuse, cv2.COLOR_YCrCb2RGB)
            img_save(rgb_fuse, img_name.split(sep='.')[0], test_out_folder)

            # 计算指标 - 读取原始图像进行指标计算
            try:
                # 读取原始红外和可见光图像用于指标计算
                ir_img = image_read_cv2(os.path.join(test_folder, "ir", img_name), mode='GRAY')
                vis_img = cv2.split(image_read_cv2(os.path.join(test_folder, "vi", img_name), mode='YCrCb'))[0]

                # 确保所有图像都是float32类型，范围0-255
                fuse_float = fuse.astype(np.float32)
                ir_img = ir_img.astype(np.float32)
                vis_img = vis_img.astype(np.float32)

                # 计算各项指标
                en_val = Evaluator.EN(fuse_float)
                sd_val = Evaluator.SD(fuse_float)
                sf_val = Evaluator.SF(fuse_float)
                ag_val = Evaluator.AG(fuse_float)
                mi_val = Evaluator.MI(fuse_float, ir_img, vis_img)
                mse_val = Evaluator.MSE(fuse_float, ir_img, vis_img)
                cc_val = Evaluator.CC(fuse_float, ir_img, vis_img)
                psnr_val = Evaluator.PSNR(fuse_float, ir_img, vis_img)
                scd_val = Evaluator.SCD(fuse_float, ir_img, vis_img)
                viff_val = Evaluator.VIFF(fuse_float, ir_img, vis_img)
                qabf_val = Evaluator.Qabf(fuse_float, ir_img, vis_img)
                ssim_val = Evaluator.SSIM(fuse_float, ir_img, vis_img)

                # 添加到数据集指标列表
                dataset_metrics['EN'].append(en_val)
                dataset_metrics['SD'].append(sd_val)
                dataset_metrics['SF'].append(sf_val)
                dataset_metrics['AG'].append(ag_val)
                dataset_metrics['MI'].append(mi_val)
                dataset_metrics['MSE'].append(mse_val)
                dataset_metrics['CC'].append(cc_val)
                dataset_metrics['PSNR'].append(psnr_val)
                dataset_metrics['SCD'].append(scd_val)
                dataset_metrics['VIFF'].append(viff_val)
                dataset_metrics['Qabf'].append(qabf_val)
                dataset_metrics['SSIM'].append(ssim_val)

            except Exception as e:
                print(f"计算指标时出错 {img_name}: {e}")
                continue
        if count > 0:
            print(f'total processing images: {count}')
            print(f'total processing time: {round(sum, 4)} seconds')
            print(f'average processing time: {round(sum / count, 4)} seconds')

            # 计算并打印当前数据集的平均指标
            print(f"\n{dataset_name} 数据集指标结果:")
            print("-" * 60)
            for metric_name in dataset_metrics:
                if dataset_metrics[metric_name]:  # 确保列表不为空
                    avg_val = np.mean(dataset_metrics[metric_name])
                    std_val = np.std(dataset_metrics[metric_name])
                    print(f"{metric_name:6s}: {avg_val:.4f} ± {std_val:.4f}")

                    # 添加到总体指标列表
                    all_metrics[metric_name].extend(dataset_metrics[metric_name])
            print("-" * 60)
        else:
            print('no images processed')

# 打印所有数据集的总体平均指标
print("\n" + "="*80)
print("所有数据集的总体指标结果:")
print("="*80)
for metric_name in all_metrics:
    if all_metrics[metric_name]:  # 确保列表不为空
        avg_val = np.mean(all_metrics[metric_name])
        std_val = np.std(all_metrics[metric_name])
        print(f"{metric_name:6s}: {avg_val:.4f} ± {std_val:.4f}")
print("="*80)
