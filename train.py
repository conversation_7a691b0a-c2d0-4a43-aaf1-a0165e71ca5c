# -*- coding: utf-8 -*-

import math
from aug_operation import Augment
from network import Restormer_Encoder, Restormer_Decoder, LongScaleFeatureExtraction, ShortScaleFeatureExtraction
from utils.dataset import H5Dataset
import os
import sys
import time
import datetime
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from utils.loss import Fusionloss, cc
import kornia
import matplotlib.pyplot as plt
import numpy as np


os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
criteria_fusion = Fusionloss()
model_str = 'CrossFuse'

# Hyper-parameters for training
num_epochs = 160 # total epoches
epoch_gap = 40  # epoches of Phase I
lr = 1e-4
weight_decay = 0
batch_size = 8
GPU_number = os.environ['CUDA_VISIBLE_DEVICES']

# 早停机制参数
early_stop_patience = 15  # 连续多少个epoch没有改善就停止
early_stop_min_delta = 1e-4  # 最小改善阈值
best_loss = float('inf')
patience_counter = 0

# Coefficients of the loss function
coeff_mse_loss_VF = 1. # alpha1
coeff_mse_loss_IF = 1.
coeff_decomp = 2.      # alpha2 and alpha4
coeff_tv = 5.
coeff_ssl_base = 0.1

clip_grad_norm_value = 0.01
optim_step = 20
optim_gamma = 0.5


# Model
device = 'cuda' if torch.cuda.is_available() else 'cpu'
DIDF_Encoder = nn.DataParallel(Restormer_Encoder()).to(device)
DIDF_Decoder = nn.DataParallel(Restormer_Decoder()).to(device)
BaseFuseLayer = nn.DataParallel(LongScaleFeatureExtraction(dim=64, num_heads=8)).to(device)
DetailFuseLayer = nn.DataParallel(ShortScaleFeatureExtraction(num_layers=1)).to(device)

# optimizer, scheduler and loss function
optimizer1 = torch.optim.Adam(
    DIDF_Encoder.parameters(), lr=lr, weight_decay=weight_decay)
optimizer2 = torch.optim.Adam(
    DIDF_Decoder.parameters(), lr=lr, weight_decay=weight_decay)
optimizer3 = torch.optim.Adam(
    BaseFuseLayer.parameters(), lr=lr, weight_decay=weight_decay)
optimizer4 = torch.optim.Adam(
    DetailFuseLayer.parameters(), lr=lr, weight_decay=weight_decay)

scheduler1 = torch.optim.lr_scheduler.StepLR(optimizer1, step_size=optim_step, gamma=optim_gamma)
scheduler2 = torch.optim.lr_scheduler.StepLR(optimizer2, step_size=optim_step, gamma=optim_gamma)
scheduler3 = torch.optim.lr_scheduler.StepLR(optimizer3, step_size=optim_step, gamma=optim_gamma)
scheduler4 = torch.optim.lr_scheduler.StepLR(optimizer4, step_size=optim_step, gamma=optim_gamma)

MSELoss = nn.MSELoss()  
L1Loss = nn.L1Loss()
Loss_ssim = kornia.losses.SSIMLoss(11, reduction='mean')


trainloader = DataLoader(H5Dataset('./dataset/RSM3.h5'),
                         batch_size=batch_size,
                         shuffle=True,
                         num_workers=0)

loader = {'train': trainloader, }
timestamp = datetime.datetime.now().strftime("%m-%d-%H-%M")

# 创建基于时间戳的模型保存目录
model_save_dir = os.path.join("models", f"CrossFuse_{timestamp}")
os.makedirs(model_save_dir, exist_ok=True)
print(f"模型将保存到: {model_save_dir}")

'''
------------------------------------------------------------------------------
Train
------------------------------------------------------------------------------
'''

step = 0
torch.backends.cudnn.benchmark = True
prev_time = time.time()

internal_aug = Augment()  # weak-aggr data augmentation

# weight adjustment
def calculate_beta(beta_base, current_step, total_steps):
    # 计算余弦衰减值
    cosine_decay = math.cos(math.pi * current_step / total_steps)
    # 调整范围到[0, 1]
    adjusted_decay = (cosine_decay + 1) / 2
    # 计算调整后的 beta 值
    beta = beta_base * adjusted_decay
    return beta

# 定义两个阶段的损失列表
losses_phase1 = []
losses_phase2 = []
epoch_losses = []  # 记录每个epoch的平均损失

for epoch in range(num_epochs):
    ''' train '''
    epoch_loss_sum = 0.0
    batch_count = 0

    for i, (data_VIS, data_IR) in enumerate(loader['train']):
        data_VIS, data_IR = data_VIS.cuda(), data_IR.cuda()
        DIDF_Encoder.train()
        DIDF_Decoder.train()
        BaseFuseLayer.train()
        DetailFuseLayer.train()

        DIDF_Encoder.zero_grad()
        DIDF_Decoder.zero_grad()
        BaseFuseLayer.zero_grad()
        DetailFuseLayer.zero_grad()

        optimizer1.zero_grad()
        optimizer2.zero_grad()
        optimizer3.zero_grad()
        optimizer4.zero_grad()

        if epoch < epoch_gap: #Phase I
            feature_V_B, feature_V_D, _ = DIDF_Encoder(data_VIS)
            feature_I_B, feature_I_D, _ = DIDF_Encoder(data_IR)
            data_VIS_hat, _ = DIDF_Decoder(data_VIS, feature_V_B, feature_V_D)
            data_IR_hat, _ = DIDF_Decoder(data_IR, feature_I_B, feature_I_D)

            cc_loss_B = cc(feature_V_B, feature_I_B)
            cc_loss_D = cc(feature_V_D, feature_I_D)
            mse_loss_V = 5 * Loss_ssim(data_VIS, data_VIS_hat) + MSELoss(data_VIS, data_VIS_hat)
            mse_loss_I = 5 * Loss_ssim(data_IR, data_IR_hat) + MSELoss(data_IR, data_IR_hat)

            Gradient_loss = L1Loss(kornia.filters.SpatialGradient()(data_VIS),
                                   kornia.filters.SpatialGradient()(data_VIS_hat))

            loss_decomp =  (cc_loss_D) ** 2/ (1.01 + cc_loss_B)

            loss = coeff_mse_loss_VF * mse_loss_V + coeff_mse_loss_IF * \
                   mse_loss_I + coeff_decomp * loss_decomp + coeff_tv * Gradient_loss

            loss.backward()
            nn.utils.clip_grad_norm_(
                DIDF_Encoder.parameters(), max_norm=clip_grad_norm_value, norm_type=2)
            nn.utils.clip_grad_norm_(
                DIDF_Decoder.parameters(), max_norm=clip_grad_norm_value, norm_type=2)
            optimizer1.step()  
            optimizer2.step()
        else:  #Phase II
            feature_V_B, feature_V_D, feature_V = DIDF_Encoder(data_VIS)
            feature_I_B, feature_I_D, feature_I = DIDF_Encoder(data_IR)
            feature_F_B = BaseFuseLayer(feature_I_B+feature_V_B)
            feature_F_D = DetailFuseLayer(feature_I_D+feature_V_D)
            data_Fuse, feature_F = DIDF_Decoder(data_VIS, feature_F_B, feature_F_D)

            ## 对融合结果进行强弱增强的自监督学习
            weak_fuse, aggr_fuse = internal_aug(data_Fuse)

            mse_loss_V = 5*Loss_ssim(data_VIS, data_Fuse) + MSELoss(data_VIS, data_Fuse)
            mse_loss_I = 5*Loss_ssim(data_IR,  data_Fuse) + MSELoss(data_IR,  data_Fuse)

            cc_loss_B = cc(feature_V_B, feature_I_B)
            cc_loss_D = cc(feature_V_D, feature_I_D)
            loss_decomp =   (cc_loss_D) ** 2 / (1.01 + cc_loss_B)  
            fusionloss, _,_  = criteria_fusion(data_VIS, data_IR, data_Fuse)
            # SSL损失
            coeff_ssl = calculate_beta(coeff_ssl_base, epoch, num_epochs-epoch_gap)
            ssl_loss = coeff_ssl * MSELoss(weak_fuse, aggr_fuse)
            loss = fusionloss + coeff_decomp * loss_decomp + ssl_loss
            loss.backward()
            nn.utils.clip_grad_norm_(
                DIDF_Encoder.parameters(), max_norm=clip_grad_norm_value, norm_type=2)
            nn.utils.clip_grad_norm_(
                DIDF_Decoder.parameters(), max_norm=clip_grad_norm_value, norm_type=2)
            nn.utils.clip_grad_norm_(
                BaseFuseLayer.parameters(), max_norm=clip_grad_norm_value, norm_type=2)
            nn.utils.clip_grad_norm_(
                DetailFuseLayer.parameters(), max_norm=clip_grad_norm_value, norm_type=2)
            optimizer1.step()
            optimizer2.step()
            optimizer3.step()
            optimizer4.step()

        # 累积损失用于早停判断
        epoch_loss_sum += loss.item()
        batch_count += 1

        # Determine approximate time left
        batches_done = epoch * len(loader['train']) + i
        batches_left = num_epochs * len(loader['train']) - batches_done
        time_left = datetime.timedelta(seconds=batches_left * (time.time() - prev_time))
        prev_time = time.time()
        sys.stdout.write(
            "\r[Epoch %d/%d] [Batch %d/%d] [loss: %f] ETA: %.10s"
            % (
                epoch,
                num_epochs,
                i,
                len(loader['train']),
                loss.item(),
                time_left,
            )
        )

    # 计算当前epoch的平均损失
    avg_epoch_loss = epoch_loss_sum / batch_count if batch_count > 0 else float('inf')
    epoch_losses.append(avg_epoch_loss)

    if epoch < epoch_gap:
        # 记录当前 epoch 的损失值
        losses_phase1.append(avg_epoch_loss)
    else:
        losses_phase2.append(avg_epoch_loss)

    # 早停机制检查（只在第二阶段进行早停）
    if epoch >= epoch_gap:
        if avg_epoch_loss < best_loss - early_stop_min_delta:
            best_loss = avg_epoch_loss
            patience_counter = 0
            # 保存最佳模型
            best_checkpoint = {
                'DIDF_Encoder': DIDF_Encoder.state_dict(),
                'DIDF_Decoder': DIDF_Decoder.state_dict(),
                'BaseFuseLayer': BaseFuseLayer.state_dict(),
                'DetailFuseLayer': DetailFuseLayer.state_dict(),
                'epoch': epoch,
                'loss': best_loss
            }
            torch.save(best_checkpoint, os.path.join(model_save_dir, 'best_model.pth'))
            print(f"\n保存最佳模型，epoch: {epoch}, loss: {best_loss:.6f}")
        else:
            patience_counter += 1

        if patience_counter >= early_stop_patience:
            print(f"\n早停触发！连续 {early_stop_patience} 个epoch没有改善")
            print(f"最佳损失: {best_loss:.6f}")
            break

    # adjust the learning rate
    scheduler1.step()  
    scheduler2.step()
    if not epoch < epoch_gap:
        scheduler3.step()
        scheduler4.step()

    if optimizer1.param_groups[0]['lr'] <= 1e-6:
        optimizer1.param_groups[0]['lr'] = 1e-6
    if optimizer2.param_groups[0]['lr'] <= 1e-6:
        optimizer2.param_groups[0]['lr'] = 1e-6
    if optimizer3.param_groups[0]['lr'] <= 1e-6:
        optimizer3.param_groups[0]['lr'] = 1e-6
    if optimizer4.param_groups[0]['lr'] <= 1e-6:
        optimizer4.param_groups[0]['lr'] = 1e-6

    # 创建一个具有两个子图的图布
    fig, axes = plt.subplots(2, 1, figsize=(8, 8))

    # 绘制阶段1的损失曲线（前40个epoch）
    axes[0].plot(range(1, len(losses_phase1) + 1), losses_phase1, label='Phase I Loss', color='blue')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Phase I Training Loss')

    # 绘制阶段2的损失曲线（后80个epoch）
    axes[1].plot(range(1, len(losses_phase2) + 1), losses_phase2, label='Phase II Loss', color='red')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('Loss')
    axes[1].set_title('Phase II Training Loss')

    # 调整布局
    plt.tight_layout()

    # 保存图像到指定目录
    plt.savefig(os.path.join(model_save_dir, 'loss_curve.png'))
    plt.close()  # 关闭图形以释放内存

# 保存最终模型
final_checkpoint = {
    'DIDF_Encoder': DIDF_Encoder.state_dict(),
    'DIDF_Decoder': DIDF_Decoder.state_dict(),
    'BaseFuseLayer': BaseFuseLayer.state_dict(),
    'DetailFuseLayer': DetailFuseLayer.state_dict(),
    'epoch': epoch,
    'final_loss': epoch_losses[-1] if epoch_losses else float('inf'),
    'training_losses': epoch_losses,
    'losses_phase1': losses_phase1,
    'losses_phase2': losses_phase2
}
torch.save(final_checkpoint, os.path.join(model_save_dir, 'final_model.pth'))

# 保存训练日志
log_info = {
    'model_name': model_str,
    'timestamp': timestamp,
    'total_epochs': epoch + 1,
    'early_stopped': patience_counter >= early_stop_patience,
    'best_loss': best_loss,
    'final_loss': epoch_losses[-1] if epoch_losses else float('inf'),
    'hyperparameters': {
        'lr': lr,
        'batch_size': batch_size,
        'num_epochs': num_epochs,
        'epoch_gap': epoch_gap,
        'early_stop_patience': early_stop_patience,
        'early_stop_min_delta': early_stop_min_delta
    }
}

import json
with open(os.path.join(model_save_dir, 'training_log.json'), 'w', encoding='utf-8') as f:
    json.dump(log_info, f, indent=2, ensure_ascii=False)

print(f"\n训练完成！")
print(f"模型保存目录: {model_save_dir}")
print(f"最终损失: {epoch_losses[-1] if epoch_losses else 'N/A':.6f}")
print(f"最佳损失: {best_loss:.6f}")
print(f"总训练轮数: {epoch + 1}/{num_epochs}")


